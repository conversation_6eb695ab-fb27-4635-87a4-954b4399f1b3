# -*- coding: utf-8 -*-
"""
Data Migration Script: Retroactive Stick Count Calculation

This script calculates and populates stick counts for existing invoice lines
that were created before the stick count functionality was implemented.

Usage:
1. Run this script from Odoo shell or as a one-time data migration
2. It will find invoice lines with cigar products that have x_cigar_stick_count = 0
3. Calculate proper stick counts based on product configuration
4. Update the invoice lines with calculated values

IMPORTANT: This should be run only once after stick count implementation.
"""

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def migrate_stick_counts(env):
    """
    Migrate stick counts for existing invoice lines.
    
    Args:
        env: Odoo environment
    """
    _logger.info("Starting stick count migration for existing invoices...")
    
    # Find all posted invoices with cigar products that have missing stick counts
    invoice_lines = env['account.move.line'].search([
        ('move_id.type', '=', 'out_invoice'),
        ('move_id.state', '=', 'posted'),
        ('product_id', '!=', False),
        '|',
        ('x_cigar_stick_count', '=', 0),
        ('x_cigar_stick_count', '=', False)
    ])
    
    updated_count = 0
    skipped_count = 0
    
    for line in invoice_lines:
        # Check if this is a cigar product
        if not (hasattr(line.product_id, 'is_cigar_category') and 
                line.product_id.is_cigar_category()):
            continue
            
        # Check if stick count field exists
        if not hasattr(line, 'x_cigar_stick_count'):
            _logger.warning(f"Invoice line {line.id} missing x_cigar_stick_count field")
            skipped_count += 1
            continue
            
        # Calculate stick count based on product configuration
        calculated_count = calculate_stick_count_for_line(line)
        
        if calculated_count > 0:
            try:
                line.x_cigar_stick_count = calculated_count
                updated_count += 1
                _logger.info(f"Updated invoice line {line.id}: {line.product_id.name} - "
                           f"Quantity: {line.quantity}, Stick Count: {calculated_count}")
            except Exception as e:
                _logger.error(f"Failed to update invoice line {line.id}: {e}")
                skipped_count += 1
        else:
            # No calculation possible, leave as quantity (current fallback behavior)
            skipped_count += 1
            _logger.info(f"Skipped invoice line {line.id}: {line.product_id.name} - "
                        f"No product configuration for stick calculation")
    
    _logger.info(f"Stick count migration completed. Updated: {updated_count}, Skipped: {skipped_count}")
    return updated_count, skipped_count


def calculate_stick_count_for_line(line):
    """
    Calculate stick count for an invoice line based on product configuration.
    
    Args:
        line: account.move.line record
        
    Returns:
        int: Calculated stick count
    """
    if not line.product_id:
        return 0
        
    # Try to find a custom field that indicates sticks per unit
    sticks_per_unit = (
        getattr(line.product_id, 'x_sticks_per_unit', None) or
        getattr(line.product_id, 'x_stick_count', None) or
        getattr(line.product_id, 'x_pieces_per_box', None) or
        getattr(line.product_id, 'x_cigars_per_box', None)
    )
    
    if sticks_per_unit and sticks_per_unit > 0:
        return int(line.quantity * sticks_per_unit)
    
    return 0


def run_migration():
    """
    Main function to run the migration.
    Call this from Odoo shell or data migration script.
    """
    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})
        return migrate_stick_counts(env)


if __name__ == '__main__':
    # This allows running the script directly if needed
    print("This script should be run from within Odoo environment.")
    print("Use: python3 odoo-bin shell -d your_database --shell-interface=ipython")
    print("Then: exec(open('scripts/migrate_stick_counts.py').read())")
    print("Finally: run_migration()")
