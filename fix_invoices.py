#!/usr/bin/env python3
"""
Simple script to fix stick counts for early invoices.

This script can be run from the Odoo interface using the "Execute Python Code" feature
in Settings > Technical > Python Code (if available) or through any Python console.

Instructions:
1. Update the invoice_data list below with your actual invoice numbers and stick counts
2. Copy and paste this entire script into Odoo's Python console
3. Run it

Example invoice_data format:
[
    ('INV/2024/0001', 40),  # Invoice name, stick count
    ('INV/2024/0002', 25),
    ('INV/2024/0003', 60),
]
"""

# UPDATE THIS LIST WITH YOUR ACTUAL INVOICE DATA
invoice_data = [
    # Replace these with your actual invoice numbers and stick counts
    ('INV/2024/0001', 40),  # Example: 2 boxes of 20 cigars = 40 sticks
    ('INV/2024/0002', 25),  # Example: 1 box of 25 cigars = 25 sticks  
    ('INV/2024/0003', 60),  # Example: 3 boxes of 20 cigars = 60 sticks
]

def fix_my_invoices():
    """
    Fix the stick counts for early invoices.
    """
    print("Starting invoice fix...")
    
    # Run the fix
    results = env['account.move.line'].fix_early_invoices_direct(invoice_data)
    
    # Print results
    print("\n=== RESULTS ===")
    for result in results:
        print(result)
    
    print(f"\nProcessed {len(invoice_data)} invoices.")
    print("Done!")

# Uncomment the line below to run the fix automatically
# fix_my_invoices()

print("""
INSTRUCTIONS:
1. Update the invoice_data list above with your actual invoice numbers and stick counts
2. Uncomment the last line: fix_my_invoices()
3. Run this script

Or simply call: fix_my_invoices()
""")
