# -*- coding: utf-8 -*-
"""
Account Move Line Extension for Tobacco Sales Reporting

This module extends account.move.line to add a manual stick count field
for cigar products that carries over from sale order lines, ensuring
consistent tracking from sales to invoicing.

IMPORTANT: NO FIELD DEFINITIONS IN PYTHON CODE!
The x_cigar_stick_count field must be created via Odoo Studio only.
This prevents interference with accounting calculations.
"""

from odoo import models, fields, api
from odoo.exceptions import UserError


class AccountMoveLine(models.Model):
    """
    Extends account.move.line to provide stick count functionality for cigars.

    This extension provides methods to work with the x_cigar_stick_count field
    that should be created via Odoo Studio or XML data files, not Python field definitions.
    """
    _inherit = 'account.move.line'

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        This method safely retrieves the stick count value, handling cases
        where the field might not exist or be accessible.

        Returns:
            int: The stick count value, or 0 if not available
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        This method safely sets the stick count value, handling cases
        where the field might not exist.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def transfer_stick_count_from_sale_line(self, sale_line):
        """
        Transfer stick count from a sale order line to this invoice line.

        This method should be called when creating invoice lines from sale orders
        to preserve the manually entered stick count.

        Args:
            sale_line: sale.order.line record
        """
        if hasattr(sale_line, 'x_cigar_stick_count') and hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = sale_line.x_cigar_stick_count

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create to automatically transfer stick count from sale order lines.
        """
        lines = super().create(vals_list)

        # Transfer stick count from sale order lines if available
        for line in lines:
            if (line.sale_line_ids and
                hasattr(line, 'x_cigar_stick_count') and
                line.product_id and
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category()):

                # Get the first sale line (usually there's only one)
                sale_line = line.sale_line_ids[0]
                if hasattr(sale_line, 'x_cigar_stick_count'):
                    stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                    if stick_count > 0:
                        line.x_cigar_stick_count = stick_count

        return lines

    def write(self, vals):
        """
        Override write to handle stick count transfer when sale_line_ids is updated.
        """
        result = super().write(vals)

        # If sale_line_ids was updated, transfer stick count
        if 'sale_line_ids' in vals:
            for line in self:
                if (line.sale_line_ids and
                    hasattr(line, 'x_cigar_stick_count') and
                    line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category()):

                    sale_line = line.sale_line_ids[0]
                    if hasattr(sale_line, 'x_cigar_stick_count'):
                        stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                        if stick_count > 0:
                            line.x_cigar_stick_count = stick_count

        return result

    def force_update_stick_count(self, stick_count):
        """
        Force update stick count for this line, bypassing any validation.

        Args:
            stick_count (int): The stick count to set
        """
        # Use SQL to directly update the field to bypass any validation
        if hasattr(self, 'x_cigar_stick_count'):
            self.env.cr.execute(
                "UPDATE account_move_line SET x_cigar_stick_count = %s WHERE id = %s",
                (stick_count, self.id)
            )
            self.env.cr.commit()
            # Refresh the record
            self.invalidate_cache(['x_cigar_stick_count'])

    def calculate_stick_count_from_product(self):
        """
        Calculate stick count for this line based on product configuration.

        This method calculates stick count using the same logic as sale order lines,
        useful for retroactively populating stick counts on existing invoices.

        Returns:
            int: Calculated stick count
        """
        if not self.product_id:
            return 0

        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        # Try to find a custom field that indicates sticks per unit
        sticks_per_unit = (
            getattr(self.product_id, 'x_sticks_per_unit', None) or
            getattr(self.product_id, 'x_stick_count', None) or
            getattr(self.product_id, 'x_pieces_per_box', None) or
            getattr(self.product_id, 'x_cigars_per_box', None)
        )

        if sticks_per_unit and sticks_per_unit > 0:
            return int(self.quantity * sticks_per_unit)

        return 0

    @api.model
    def migrate_missing_stick_counts(self):
        """
        Retroactively calculate and populate stick counts for existing invoice lines.

        This method finds invoice lines with cigar products that have missing or zero
        stick counts and calculates proper values based on product configuration.

        Returns:
            dict: Summary of migration results
        """
        # Find posted invoice lines with cigar products that have missing stick counts
        lines_to_update = self.search([
            ('move_id.type', '=', 'out_invoice'),
            ('move_id.state', '=', 'posted'),
            ('product_id', '!=', False),
            '|',
            ('x_cigar_stick_count', '=', 0),
            ('x_cigar_stick_count', '=', False)
        ])

        updated_count = 0
        skipped_count = 0
        results = []

        for line in lines_to_update:
            # Check if this is a cigar product
            if not (hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category()):
                continue

            # Check if stick count field exists
            if not hasattr(line, 'x_cigar_stick_count'):
                skipped_count += 1
                continue

            # Calculate stick count
            calculated_count = line.calculate_stick_count_from_product()

            if calculated_count > 0:
                try:
                    line.x_cigar_stick_count = calculated_count
                    updated_count += 1
                    results.append({
                        'invoice': line.move_id.name,
                        'product': line.product_id.name,
                        'quantity': line.quantity,
                        'calculated_stick_count': calculated_count,
                        'status': 'updated'
                    })
                except Exception as e:
                    skipped_count += 1
                    results.append({
                        'invoice': line.move_id.name,
                        'product': line.product_id.name,
                        'quantity': line.quantity,
                        'error': str(e),
                        'status': 'error'
                    })
            else:
                skipped_count += 1
                results.append({
                    'invoice': line.move_id.name,
                    'product': line.product_id.name,
                    'quantity': line.quantity,
                    'status': 'skipped - no product configuration'
                })

        return {
            'updated_count': updated_count,
            'skipped_count': skipped_count,
            'total_processed': len(lines_to_update),
            'details': results
        }

    @api.model
    def quick_fix_stick_counts(self, invoice_updates):
        """
        Quick method to update stick counts for specific invoices.

        Args:
            invoice_updates (list): List of dicts with 'invoice_name', 'product_name', 'stick_count'

        Example usage:
            updates = [
                {'invoice_name': 'INV/2024/0001', 'product_name': 'Cigar Product', 'stick_count': 40},
                {'invoice_name': 'INV/2024/0002', 'product_name': 'Another Cigar', 'stick_count': 25},
            ]
            env['account.move.line'].quick_fix_stick_counts(updates)
        """
        results = []

        for update in invoice_updates:
            invoice_name = update.get('invoice_name')
            product_name = update.get('product_name')
            stick_count = update.get('stick_count', 0)

            # Find the invoice
            invoice = self.env['account.move'].search([
                ('name', '=', invoice_name),
                ('type', '=', 'out_invoice')
            ], limit=1)

            if not invoice:
                results.append(f"❌ Invoice {invoice_name} not found")
                continue

            # Find the product line
            line = invoice.invoice_line_ids.filtered(
                lambda l: product_name.lower() in l.product_id.name.lower()
            )

            if not line:
                results.append(f"❌ Product '{product_name}' not found in invoice {invoice_name}")
                continue

            if len(line) > 1:
                results.append(f"⚠️ Multiple products matching '{product_name}' in {invoice_name}, updating first one")
                line = line[0]
            else:
                line = line[0]

            # Force update the stick count
            try:
                line.force_update_stick_count(stick_count)
                results.append(f"✅ Updated {invoice_name}: {line.product_id.name} → {stick_count} sticks")
            except Exception as e:
                results.append(f"❌ Failed to update {invoice_name}: {str(e)}")

        return results

    @api.model
    def fix_early_invoices_direct(self, invoice_data):
        """
        Direct method to fix stick counts for specific invoices.

        Args:
            invoice_data (list): List of tuples (invoice_name, stick_count)

        Example:
            env['account.move.line'].fix_early_invoices_direct([
                ('INV/2024/0001', 40),
                ('INV/2024/0002', 25),
                ('INV/2024/0003', 60)
            ])
        """
        results = []

        for invoice_name, stick_count in invoice_data:
            # Find the invoice
            invoice = self.env['account.move'].search([
                ('name', '=', invoice_name),
                ('type', '=', 'out_invoice')
            ], limit=1)

            if not invoice:
                results.append(f"❌ Invoice {invoice_name} not found")
                continue

            # Find cigar product lines
            cigar_lines = []
            for line in invoice.invoice_line_ids:
                if (line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category()):
                    cigar_lines.append(line)

            if not cigar_lines:
                results.append(f"⚠️ No cigar products found in {invoice_name}")
                continue

            # Update the first cigar line (assuming one cigar product per invoice)
            line = cigar_lines[0]

            try:
                # Direct SQL update to bypass all validation
                self.env.cr.execute("""
                    UPDATE account_move_line
                    SET x_cigar_stick_count = %s
                    WHERE id = %s
                """, (stick_count, line.id))

                # Commit the change
                self.env.cr.commit()

                results.append(f"✅ Updated {invoice_name}: {line.product_id.name} → {stick_count} sticks")

            except Exception as e:
                results.append(f"❌ Failed to update {invoice_name}: {str(e)}")

        return results







