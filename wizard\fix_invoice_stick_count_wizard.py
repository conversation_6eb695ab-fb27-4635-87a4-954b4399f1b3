# -*- coding: utf-8 -*-
"""
Fix Invoice Stick Count Wizard

This wizard helps fix existing posted invoices that are missing stick count data
by transferring the stick count from their linked sale order lines.
"""

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class FixInvoiceStickCountWizard(models.TransientModel):
    """
    Wizard to fix stick count on existing invoice lines.
    
    This wizard finds invoice lines for cigar products that have linked sale order lines
    with stick count data but are missing stick count on the invoice line.
    """
    _name = 'fix.invoice.stick.count.wizard'
    _description = 'Fix Invoice Stick Count Wizard'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda _: fields.Date.today().replace(day=1),
        help="Start date to search for invoices to fix"
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today,
        help="End date to search for invoices to fix"
    )
    partner_ids = fields.Many2many(
        'res.partner',
        string='Customers',
        help="Select specific customers. Leave empty to include all customers."
    )
    only_posted = fields.Boolean(
        string='Only Posted Invoices',
        default=True,
        help="Only process posted invoices"
    )
    preview_mode = fields.Boolean(
        string='Preview Mode',
        default=True,
        help="When enabled, shows what would be updated without making changes"
    )
    
    # Results fields
    found_lines_count = fields.Integer(
        string='Lines Found',
        readonly=True,
        help="Number of invoice lines that need stick count updates"
    )
    result_message = fields.Text(
        string='Results',
        readonly=True
    )

    def action_find_lines(self):
        """
        Find invoice lines that need stick count updates.
        
        Returns:
            dict: Action to reload the wizard with results
        """
        lines_to_fix = self._find_lines_needing_fix()
        
        self.found_lines_count = len(lines_to_fix)
        
        if lines_to_fix:
            # Create summary message
            invoice_count = len(lines_to_fix.mapped('move_id'))
            customer_count = len(lines_to_fix.mapped('partner_id'))
            
            message = _(
                "Found %d invoice lines across %d invoices from %d customers that need stick count updates.\n\n"
                "These lines have cigar products with linked sale order lines containing stick count data, "
                "but the invoice lines are missing stick count values.\n\n"
                "Date Range: %s to %s"
            ) % (
                len(lines_to_fix),
                invoice_count,
                customer_count,
                self.date_from.strftime('%m/%d/%Y'),
                self.date_to.strftime('%m/%d/%Y')
            )
        else:
            message = _(
                "No invoice lines found that need stick count updates in the selected date range.\n\n"
                "Date Range: %s to %s"
            ) % (
                self.date_from.strftime('%m/%d/%Y'),
                self.date_to.strftime('%m/%d/%Y')
            )
        
        self.result_message = message
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'fix.invoice.stick.count.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def action_fix_stick_counts(self):
        """
        Fix stick counts on the found invoice lines.
        
        Returns:
            dict: Action showing results
        """
        if self.preview_mode:
            raise UserError(_('Cannot fix stick counts in preview mode. Disable preview mode first.'))
        
        lines_to_fix = self._find_lines_needing_fix()
        
        if not lines_to_fix:
            raise UserError(_('No lines found that need fixing.'))
        
        # Update the lines
        result = lines_to_fix.update_stick_count_from_sale_lines()
        
        # Update result message
        message = _(
            "Stick count update completed!\n\n"
            "Updated: %d lines\n"
            "Skipped: %d lines\n"
            "Total processed: %d lines\n\n"
            "The stick counts have been transferred from sale order lines to invoice lines."
        ) % (
            result['updated'],
            result['skipped'],
            result['total_processed']
        )
        
        self.result_message = message
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'fix.invoice.stick.count.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def _find_lines_needing_fix(self):
        """
        Find invoice lines that need stick count updates.
        
        Returns:
            recordset: account.move.line records that need fixing
        """
        # Build domain for invoices
        domain = [
            ('type', '=', 'out_invoice'),
            ('invoice_date', '>=', self.date_from),
            ('invoice_date', '<=', self.date_to),
        ]
        
        if self.only_posted:
            domain.append(('state', '=', 'posted'))
        
        if self.partner_ids:
            domain.append(('partner_id', 'in', self.partner_ids.ids))
        
        invoices = self.env['account.move'].search(domain)
        
        # Find lines that need fixing
        lines_needing_fix = self.env['account.move.line']
        
        for invoice in invoices:
            for line in invoice.invoice_line_ids:
                # Check if this line needs fixing
                if self._line_needs_fixing(line):
                    lines_needing_fix |= line
        
        return lines_needing_fix

    def _line_needs_fixing(self, line):
        """
        Check if an invoice line needs stick count fixing.
        
        Args:
            line: account.move.line record
            
        Returns:
            bool: True if line needs fixing
        """
        # Must be a cigar product
        if not (line.product_id and 
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category()):
            return False
        
        # Must have x_cigar_stick_count field
        if not hasattr(line, 'x_cigar_stick_count'):
            return False
        
        # Must have linked sale order lines
        if not line.sale_line_ids:
            return False
        
        # Current stick count must be 0 or empty
        current_stick_count = getattr(line, 'x_cigar_stick_count', 0)
        if current_stick_count > 0:
            return False
        
        # Sale line must have stick count data
        sale_line = line.sale_line_ids[0]
        if not hasattr(sale_line, 'x_cigar_stick_count'):
            return False
        
        sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
        if sale_stick_count <= 0:
            return False
        
        return True
