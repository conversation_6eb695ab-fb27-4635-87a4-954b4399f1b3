#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to fix missing stick counts on existing invoice lines.

This script can be run from the Odoo shell to update existing posted invoices
that are missing cigar stick count data by transferring the data from their
linked sale order lines.

Usage:
1. From Odoo shell:
   python3 odoo-bin shell -d your_database -c your_config.conf
   
2. In the shell:
   exec(open('/path/to/this/script.py').read())
   
3. Or run specific functions:
   result = fix_all_missing_stick_counts()
   print(result)
"""

from datetime import date, timedelta


def fix_all_missing_stick_counts(date_from=None, date_to=None, dry_run=True):
    """
    Fix all missing stick counts for posted invoices.
    
    Args:
        date_from (date): Start date (default: 3 months ago)
        date_to (date): End date (default: today)
        dry_run (bool): If True, only shows what would be updated
        
    Returns:
        dict: Summary of results
    """
    if not date_from:
        date_from = date.today() - timedelta(days=90)  # 3 months ago
    if not date_to:
        date_to = date.today()
    
    print(f"Searching for invoices from {date_from} to {date_to}")
    print(f"Dry run mode: {dry_run}")
    
    # Use the bulk fix method
    AccountMoveLine = env['account.move.line']
    
    if dry_run:
        # Find lines that would be updated
        domain = [
            ('move_id.type', '=', 'out_invoice'),
            ('move_id.state', '=', 'posted'),
            ('move_id.invoice_date', '>=', date_from),
            ('move_id.invoice_date', '<=', date_to),
        ]
        
        all_lines = AccountMoveLine.search(domain)
        lines_to_fix = env['account.move.line']
        
        for line in all_lines:
            if (line.product_id and 
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category() and
                hasattr(line, 'x_cigar_stick_count') and
                line.sale_line_ids):
                
                current_stick_count = getattr(line, 'x_cigar_stick_count', 0)
                if current_stick_count == 0:
                    sale_line = line.sale_line_ids[0]
                    if hasattr(sale_line, 'x_cigar_stick_count'):
                        sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                        if sale_stick_count > 0:
                            lines_to_fix |= line
        
        result = {
            'updated': 0,
            'skipped': 0,
            'total_processed': len(lines_to_fix),
            'invoices_processed': len(lines_to_fix.mapped('move_id')),
            'customers_affected': len(lines_to_fix.mapped('partner_id')),
            'dry_run': True
        }
        
        print(f"DRY RUN RESULTS:")
        print(f"Lines that would be updated: {result['total_processed']}")
        print(f"Invoices affected: {result['invoices_processed']}")
        print(f"Customers affected: {result['customers_affected']}")
        
    else:
        # Actually fix the lines
        result = AccountMoveLine.fix_missing_stick_counts_bulk(
            date_from=date_from,
            date_to=date_to,
            only_posted=True
        )
        result['dry_run'] = False
        
        print(f"ACTUAL UPDATE RESULTS:")
        print(f"Lines updated: {result['updated']}")
        print(f"Lines skipped: {result['skipped']}")
        print(f"Total processed: {result['total_processed']}")
        print(f"Invoices processed: {result['invoices_processed']}")
        print(f"Customers affected: {result['customers_affected']}")
    
    return result


def fix_specific_invoice(invoice_number):
    """
    Fix stick counts for a specific invoice.
    
    Args:
        invoice_number (str): Invoice number to fix
        
    Returns:
        dict: Summary of results
    """
    invoice = env['account.move'].search([('name', '=', invoice_number)], limit=1)
    
    if not invoice:
        print(f"Invoice {invoice_number} not found")
        return {'error': 'Invoice not found'}
    
    print(f"Processing invoice: {invoice.name} for customer: {invoice.partner_id.name}")
    
    # Find cigar lines that need fixing
    lines_to_fix = env['account.move.line']
    
    for line in invoice.invoice_line_ids:
        if (line.product_id and 
            hasattr(line.product_id, 'is_cigar_category') and
            line.product_id.is_cigar_category() and
            hasattr(line, 'x_cigar_stick_count') and
            line.sale_line_ids):
            
            current_stick_count = getattr(line, 'x_cigar_stick_count', 0)
            if current_stick_count == 0:
                sale_line = line.sale_line_ids[0]
                if hasattr(sale_line, 'x_cigar_stick_count'):
                    sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                    if sale_stick_count > 0:
                        lines_to_fix |= line
                        print(f"  Line: {line.product_id.name} - Will update from {current_stick_count} to {sale_stick_count}")
    
    if lines_to_fix:
        result = lines_to_fix.update_stick_count_from_sale_lines()
        print(f"Updated {result['updated']} lines")
    else:
        result = {'updated': 0, 'skipped': 0, 'total_processed': 0}
        print("No lines needed updating")
    
    return result


def find_invoices_missing_stick_counts(date_from=None, date_to=None, limit=10):
    """
    Find invoices that are missing stick counts.
    
    Args:
        date_from (date): Start date
        date_to (date): End date  
        limit (int): Maximum number of invoices to show
        
    Returns:
        list: List of invoice information
    """
    if not date_from:
        date_from = date.today() - timedelta(days=30)
    if not date_to:
        date_to = date.today()
    
    domain = [
        ('type', '=', 'out_invoice'),
        ('state', '=', 'posted'),
        ('invoice_date', '>=', date_from),
        ('invoice_date', '<=', date_to),
    ]
    
    invoices = env['account.move'].search(domain)
    problem_invoices = []
    
    for invoice in invoices:
        has_cigar_lines = False
        missing_stick_count = False
        
        for line in invoice.invoice_line_ids:
            if (line.product_id and 
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category()):
                has_cigar_lines = True
                
                if hasattr(line, 'x_cigar_stick_count'):
                    stick_count = getattr(line, 'x_cigar_stick_count', 0)
                    if stick_count == 0 and line.sale_line_ids:
                        sale_line = line.sale_line_ids[0]
                        if hasattr(sale_line, 'x_cigar_stick_count'):
                            sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                            if sale_stick_count > 0:
                                missing_stick_count = True
                                break
        
        if has_cigar_lines and missing_stick_count:
            problem_invoices.append({
                'invoice_number': invoice.name,
                'customer': invoice.partner_id.name,
                'date': invoice.invoice_date,
                'amount': invoice.amount_total
            })
            
            if len(problem_invoices) >= limit:
                break
    
    print(f"Found {len(problem_invoices)} invoices with missing stick counts:")
    for inv in problem_invoices:
        print(f"  {inv['invoice_number']} - {inv['customer']} - {inv['date']} - ${inv['amount']}")
    
    return problem_invoices


# Example usage when running from shell:
if __name__ == '__main__':
    print("Tobacco Sales Stick Count Fix Script")
    print("=====================================")
    print()
    print("Available functions:")
    print("1. fix_all_missing_stick_counts(date_from, date_to, dry_run=True)")
    print("2. fix_specific_invoice(invoice_number)")
    print("3. find_invoices_missing_stick_counts(date_from, date_to, limit=10)")
    print()
    print("Example:")
    print("result = fix_all_missing_stick_counts(dry_run=True)")
    print("print(result)")
