<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Tobacco Sales Report Wizard Form View -->
        <record id="view_tobacco_sales_report_wizard_form" model="ir.ui.view">
            <field name="name">tobacco.sales.report.wizard.form</field>
            <field name="model">tobacco.sales.report.wizard</field>
            <field name="arch" type="xml">
                <form string="Generate Tobacco Sales Report">
                    <div class="oe_title">
                        <h1>
                            Monthly Tobacco Sales Report
                        </h1>
                        <p class="oe_grey">
                            Generate a comprehensive Excel report of tobacco and cigar sales including customer details,
                            federal IDs, invoice information, and sales amounts. You can filter by specific customers,
                            choose to include tobacco products, cigars, or both, and select the user to filter by invoice creator.
                            For official reports, use <EMAIL> to exclude test data.
                        </p>
                    </div>
                    
                    <group>
                        <group string="Report Period">
                            <field name="date_from" widget="date"/>
                            <field name="date_to" widget="date"/>
                        </group>
                        <group string="Filters">
                            <field name="product_type"/>
                            <field name="user_id"
                                   domain="[('login', 'in', ['<EMAIL>', '<EMAIL>'])]"
                                   help="Select user to filter invoices by creator. Use <EMAIL> for official reports (excludes test data)."/>
                            <field name="partner_ids" widget="many2many_tags"
                                   placeholder="Leave empty for all customers"/>
                        </group>
                    </group>
                    
                    <div class="oe_clear">
                        <br/>
                        <p class="oe_grey">
                            <strong>Report will include:</strong><br/>
                            • Customer Name and Federal ID<br/>
                            • Sale Date and Invoice Date<br/>
                            • Invoice Number<br/>
                            • Customer Address<br/>
                            • Total Tobacco Sales Amount<br/>
                            • Premium Cigar Stick Count and Amount<br/>
                            • Total Ounces (for pipe tobacco products)
                        </p>
                    </div>
                    
                    <footer>
                        <button name="action_generate_report" 
                                string="Generate Excel Report" 
                                type="object" 
                                class="btn-primary"
                                icon="fa-file-excel-o"/>
                        <button name="action_preview_data" 
                                string="Preview Data" 
                                type="object" 
                                class="btn-secondary"
                                icon="fa-eye"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Tobacco Sales Report Wizard Action -->
        <record id="action_tobacco_sales_report_wizard" model="ir.actions.act_window">
            <field name="name">Generate Tobacco Sales Report</field>
            <field name="res_model">tobacco.sales.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="view_tobacco_sales_report_wizard_form"/>
        </record>

    </data>
</odoo>
