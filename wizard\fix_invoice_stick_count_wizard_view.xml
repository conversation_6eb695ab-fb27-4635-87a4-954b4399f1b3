<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Fix Invoice Stick Count Wizard Form View -->
        <record id="view_fix_invoice_stick_count_wizard_form" model="ir.ui.view">
            <field name="name">fix.invoice.stick.count.wizard.form</field>
            <field name="model">fix.invoice.stick.count.wizard</field>
            <field name="arch" type="xml">
                <form string="Fix Invoice Stick Counts">
                    <div class="oe_title">
                        <h1>
                            Fix Invoice Stick Counts
                        </h1>
                        <p class="oe_grey">
                            This tool helps fix existing posted invoices that are missing cigar stick count data.
                            It will transfer stick counts from linked sale order lines to invoice lines where the data is missing.
                        </p>
                    </div>
                    
                    <group>
                        <group string="Search Criteria">
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="partner_ids" widget="many2many_tags"/>
                            <field name="only_posted"/>
                        </group>
                        <group string="Options">
                            <field name="preview_mode"/>
                        </group>
                    </group>
                    
                    <group string="Results" attrs="{'invisible': [('found_lines_count', '=', 0)]}">
                        <field name="found_lines_count"/>
                        <field name="result_message" widget="text" readonly="1"/>
                    </group>
                    
                    <footer>
                        <button name="action_find_lines" 
                                string="Find Lines to Fix" 
                                type="object" 
                                class="btn-primary"/>
                        <button name="action_fix_stick_counts" 
                                string="Fix Stick Counts" 
                                type="object" 
                                class="btn-success"
                                attrs="{'invisible': ['|', ('found_lines_count', '=', 0), ('preview_mode', '=', True)]}"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Action to open the wizard -->
        <record id="action_fix_invoice_stick_count_wizard" model="ir.actions.act_window">
            <field name="name">Fix Invoice Stick Counts</field>
            <field name="res_model">fix.invoice.stick.count.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{}</field>
        </record>
        
        <!-- Menu item -->
        <menuitem id="menu_fix_invoice_stick_count"
                  name="Fix Invoice Stick Counts"
                  parent="menu_tobacco_sales_reports"
                  action="action_fix_invoice_stick_count_wizard"
                  sequence="20"/>
                  
    </data>
</odoo>
