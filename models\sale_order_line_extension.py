# -*- coding: utf-8 -*-
"""
Sale Order Line Extension for Tobacco Sales Reporting

This module provides utility methods for working with cigar stick count
on sale order lines. The x_cigar_stick_count field must be created via
Odoo Studio to avoid any interference with accounting calculations.

NO FIELD DEFINITIONS - Fields must be created via Studio only!
"""

from odoo import models, api


class SaleOrderLine(models.Model):
    """
    Extends sale.order.line to provide utility methods for cigar stick count.

    This extension only provides helper methods and does NOT define any fields.
    The x_cigar_stick_count field must be created via Odoo Studio.
    """
    _inherit = 'sale.order.line'

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        Returns:
            int: The stick count value, or 0 if field doesn't exist
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def calculate_cigar_stick_count(self):
        """
        Calculate the stick count based on product configuration.

        Returns:
            int: Calculated stick count
        """
        if not (self.product_id and hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        # Try to find a custom field that indicates sticks per unit
        sticks_per_unit = (
            getattr(self.product_id, 'x_sticks_per_unit', None) or
            getattr(self.product_id, 'x_stick_count', None) or
            getattr(self.product_id, 'x_pieces_per_box', None) or
            getattr(self.product_id, 'x_cigars_per_box', None) or
            1  # Default to 1 if no custom field found
        )

        return int(self.product_uom_qty * sticks_per_unit)

    @api.onchange('product_id', 'product_uom_qty')
    def _onchange_product_auto_calculate_stick_count(self):
        """
        Auto-calculate stick count when product or quantity changes.
        """
        if not self.product_id:
            return

        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return

        if not hasattr(self, 'x_cigar_stick_count'):
            return

        calculated_count = self.calculate_cigar_stick_count()
        if calculated_count > 0:
            self.x_cigar_stick_count = calculated_count

    def force_calculate_stick_count(self):
        """
        Force calculate and set stick count regardless of current value.

        This method can be used for testing or manual recalculation.
        Returns the calculated count for debugging.
        """
        if not self.product_id:
            return 0

        if not (hasattr(self.product_id, 'is_cigar_category') and
                self.product_id.is_cigar_category()):
            return 0

        calculated_count = self.calculate_cigar_stick_count()

        # Set the value if field exists
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = calculated_count

        return calculated_count

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create to automatically calculate stick count for cigar products.

        This handles cases where sale order lines are created programmatically
        (like from POS orders) without triggering onchange methods.
        """
        lines = super().create(vals_list)

        # Calculate stick count for cigar products
        for line in lines:
            if (line.product_id and
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category() and
                hasattr(line, 'x_cigar_stick_count')):

                # Only calculate if stick count is 0 (not already set)
                current_count = getattr(line, 'x_cigar_stick_count', 0)
                if current_count == 0:
                    calculated_count = line.calculate_cigar_stick_count()
                    if calculated_count > 0:
                        line.x_cigar_stick_count = calculated_count

        return lines

    def write(self, vals):
        """
        Override write to recalculate stick count when product or quantity changes.

        This handles programmatic updates that don't trigger onchange methods.
        """
        result = super().write(vals)

        # Recalculate if product_id or product_uom_qty changed
        if 'product_id' in vals or 'product_uom_qty' in vals:
            for line in self:
                if (line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category() and
                    hasattr(line, 'x_cigar_stick_count')):

                    calculated_count = line.calculate_cigar_stick_count()
                    if calculated_count > 0:
                        line.x_cigar_stick_count = calculated_count

        return result


