# -*- coding: utf-8 -*-
"""
Fix Early Invoices Wizard

Simple wizard to manually fix stick counts for early invoices.
"""

from odoo import models, fields, api


class FixEarlyInvoicesWizard(models.TransientModel):
    """
    Wizard to fix stick counts for specific early invoices.
    """
    _name = 'fix.early.invoices.wizard'
    _description = 'Fix Early Invoices Wizard'

    # Invoice 1
    invoice1_name = fields.Char(
        string="Invoice 1 Number",
        help="Enter the invoice number (e.g., INV/2024/0001)"
    )
    invoice1_stick_count = fields.Integer(
        string="Stick Count",
        help="Enter the correct number of cigar sticks"
    )

    # Invoice 2
    invoice2_name = fields.Char(
        string="Invoice 2 Number",
        help="Enter the invoice number (e.g., INV/2024/0002)"
    )
    invoice2_stick_count = fields.Integer(
        string="Stick Count",
        help="Enter the correct number of cigar sticks"
    )

    # Invoice 3
    invoice3_name = fields.Char(
        string="Invoice 3 Number",
        help="Enter the invoice number (e.g., INV/2024/0003)"
    )
    invoice3_stick_count = fields.Integer(
        string="Stick Count",
        help="Enter the correct number of cigar sticks"
    )

    info_text = fields.Html(
        string="Instructions",
        default="""
        <p><strong>Fix Early Invoices</strong></p>
        <p>Use this wizard to fix stick counts for your early invoices that were created before the stick count feature was implemented.</p>
        <ul>
            <li>Enter the exact invoice number (e.g., INV/2024/0001)</li>
            <li>Enter the correct number of individual cigar sticks</li>
            <li>Leave fields blank if you don't need to fix that invoice</li>
        </ul>
        <p><strong>Example:</strong> If you sold 2 boxes of 20 cigars each, enter 40 for stick count.</p>
        """,
        readonly=True
    )

    def action_fix_invoices(self):
        """
        Fix the stick counts for the specified invoices.
        
        Returns:
            dict: Action to show results
        """
        # Collect invoice data from the form
        invoice_data = []
        
        if self.invoice1_name and self.invoice1_stick_count > 0:
            invoice_data.append((self.invoice1_name, self.invoice1_stick_count))
            
        if self.invoice2_name and self.invoice2_stick_count > 0:
            invoice_data.append((self.invoice2_name, self.invoice2_stick_count))
            
        if self.invoice3_name and self.invoice3_stick_count > 0:
            invoice_data.append((self.invoice3_name, self.invoice3_stick_count))
        
        if not invoice_data:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Data Entered',
                    'message': 'Please enter at least one invoice number and stick count.',
                    'type': 'warning',
                    'sticky': True,
                }
            }
        
        try:
            # Run the fix
            results = self.env['account.move.line'].fix_early_invoices_direct(invoice_data)
            
            # Prepare results message
            message = f"Processed {len(invoice_data)} invoices:\n\n"
            message += "\n".join(results)
            
            # Count successful updates
            success_count = len([r for r in results if r.startswith("✅")])
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': f'Fix Results - {success_count} Updated',
                    'message': message,
                    'type': 'success' if success_count > 0 else 'warning',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Fix Error',
                    'message': f"Error fixing invoices: {str(e)}",
                    'type': 'danger',
                    'sticky': True,
                }
            }
