# Stick Count Fix Guide

## Problem Description

The issue is that already posted invoices are missing cigar stick count data, even though the corresponding sale order lines have the stick count properly filled in. This causes two problems:

1. **Invoice lines show 0 stick count** - Even though the sale order had the correct stick count
2. **Reports show incorrect data** - The tobacco sales report uses invoice line data, so missing stick counts affect report accuracy

## Root Cause

The stick count transfer from sale order lines to invoice lines happens during invoice creation. However, for invoices that were created before the stick count functionality was fully implemented, or where the transfer failed for some reason, the invoice lines are missing this data.

## Solution Components

### 1. Enhanced Account Move Line Extension

**File**: `models/account_move_line_extension.py`

Added new methods:
- `update_stick_count_from_sale_lines()` - Updates stick count for existing invoice lines
- `fix_missing_stick_counts_bulk()` - Bulk fix method for multiple invoices

### 2. Fix Invoice Stick Count Wizard

**Files**: 
- `wizard/fix_invoice_stick_count_wizard.py`
- `wizard/fix_invoice_stick_count_wizard_view.xml`

A user-friendly wizard that allows:
- Selecting date range for invoices to fix
- Filtering by specific customers
- Preview mode to see what would be updated
- Actual fix mode to perform the updates

### 3. Utility Script

**File**: `scripts/fix_invoice_stick_counts.py`

A Python script that can be run from the Odoo shell for:
- Bulk fixing all missing stick counts
- Fixing specific invoices by number
- Finding invoices that need fixing

## How to Use

### Option 1: Using the Wizard (Recommended)

1. Go to **Sales > Reports > Fix Invoice Stick Counts**
2. Set the date range for invoices you want to check
3. Optionally filter by specific customers
4. Click **"Find Lines to Fix"** to see what would be updated
5. Disable **"Preview Mode"** 
6. Click **"Fix Stick Counts"** to perform the actual updates

### Option 2: Using the Utility Script

1. Access Odoo shell:
   ```bash
   python3 odoo-bin shell -d your_database -c your_config.conf
   ```

2. Load the script:
   ```python
   exec(open('/path/to/scripts/fix_invoice_stick_counts.py').read())
   ```

3. Run functions:
   ```python
   # Preview what would be fixed (dry run)
   result = fix_all_missing_stick_counts(dry_run=True)
   print(result)
   
   # Actually fix the issues
   result = fix_all_missing_stick_counts(dry_run=False)
   print(result)
   
   # Fix a specific invoice
   result = fix_specific_invoice('INV/2024/0001')
   print(result)
   
   # Find problematic invoices
   invoices = find_invoices_missing_stick_counts()
   ```

### Option 3: Direct Method Call

```python
# From Odoo shell or in custom code
AccountMoveLine = env['account.move.line']
from datetime import date, timedelta

# Fix all invoices from last 3 months
date_from = date.today() - timedelta(days=90)
date_to = date.today()

result = AccountMoveLine.fix_missing_stick_counts_bulk(
    date_from=date_from,
    date_to=date_to,
    only_posted=True
)

print(f"Updated {result['updated']} lines across {result['invoices_processed']} invoices")
```

## What Gets Fixed

The fix process:

1. **Finds invoice lines** that meet these criteria:
   - Product is a cigar (has `is_cigar_category()` method returning True)
   - Invoice line has `x_cigar_stick_count` field
   - Invoice line is linked to sale order lines (`sale_line_ids`)
   - Current stick count is 0 or empty
   - Linked sale order line has stick count > 0

2. **Transfers stick count** from sale order line to invoice line

3. **Preserves existing data** - Only updates lines where stick count is missing (0 or empty)

## Safety Features

- **Preview mode** - See what would be updated before making changes
- **Only updates missing data** - Existing stick counts are preserved
- **Validation** - Only processes valid cigar products with proper links
- **Audit trail** - Returns detailed summary of what was updated

## Report Impact

After fixing stick counts:
- **Tobacco Sales Report** will show accurate cigar stick counts
- **Historical data** becomes consistent
- **Compliance reporting** improves accuracy

## Prevention

To prevent this issue in the future:
- The enhanced `account.move.line` extension now has better transfer logic
- Stick count transfer happens during both `create()` and `write()` operations
- Manual verification can be done using the wizard periodically

## Troubleshooting

### If stick counts still don't transfer:

1. **Check field existence**:
   ```python
   # Verify fields exist
   sale_line = env['sale.order.line'].browse(123)
   invoice_line = env['account.move.line'].browse(456)
   
   print(f"Sale line has field: {hasattr(sale_line, 'x_cigar_stick_count')}")
   print(f"Invoice line has field: {hasattr(invoice_line, 'x_cigar_stick_count')}")
   ```

2. **Check product classification**:
   ```python
   product = env['product.product'].browse(789)
   print(f"Is cigar: {product.is_cigar_category()}")
   ```

3. **Check sale line links**:
   ```python
   invoice_line = env['account.move.line'].browse(456)
   print(f"Linked sale lines: {invoice_line.sale_line_ids}")
   ```

### If wizard doesn't appear in menu:

1. Update the module: **Apps > Tobacco Sales Excel Report > Upgrade**
2. Check user permissions: User must have Sales access
3. Verify menu structure in **Settings > Technical > User Interface > Menu Items**
