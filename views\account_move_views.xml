<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Add Cigar Stick Count field to Invoice Form View -->
        <record id="view_move_form_cigar_stick_count" model="ir.ui.view">
            <field name="name">account.move.form.cigar.stick.count</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <field name="quantity" position="after">
                    <field name="x_cigar_stick_count"
                           string="Cigar Sticks"
                           optional="show"
                           help="Manual entry for actual number of cigar sticks being invoiced (only for cigar products)"/>
                </field>
            </field>
        </record>

        <!-- Add Stick Count Migration Button to Invoice Form View -->
        <record id="view_move_form_stick_count_migration" model="ir.ui.view">
            <field name="name">account.move.form.stick.count.migration</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <header position="inside">
                    <button name="action_fix_early_invoices"
                            type="object"
                            string="Fix Early Invoices"
                            class="btn-warning"
                            help="Quick fix for early invoices - calculates stick counts for the first few invoices with cigar products"/>
                </header>
            </field>
        </record>

        <!-- Add Stick Count Migration Action to Action Menu -->
        <record id="action_migrate_stick_counts" model="ir.actions.server">
            <field name="name">Migrate Stick Counts</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">
action = records.action_migrate_stick_counts()
            </field>
        </record>

    </data>
</odoo>
