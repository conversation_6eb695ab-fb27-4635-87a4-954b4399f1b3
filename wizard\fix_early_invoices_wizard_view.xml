<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Fix Early Invoices Wizard Form View -->
        <record id="view_fix_early_invoices_wizard_form" model="ir.ui.view">
            <field name="name">fix.early.invoices.wizard.form</field>
            <field name="model">fix.early.invoices.wizard</field>
            <field name="arch" type="xml">
                <form string="Fix Early Invoices">
                    <sheet>
                        <div class="oe_title">
                            <h1>Fix Early Invoices</h1>
                        </div>
                        
                        <group>
                            <field name="info_text" nolabel="1"/>
                        </group>
                        
                        <group string="Invoice 1">
                            <field name="invoice1_name" placeholder="e.g., INV/2024/0001"/>
                            <field name="invoice1_stick_count" placeholder="e.g., 40"/>
                        </group>
                        
                        <group string="Invoice 2">
                            <field name="invoice2_name" placeholder="e.g., INV/2024/0002"/>
                            <field name="invoice2_stick_count" placeholder="e.g., 25"/>
                        </group>
                        
                        <group string="Invoice 3">
                            <field name="invoice3_name" placeholder="e.g., INV/2024/0003"/>
                            <field name="invoice3_stick_count" placeholder="e.g., 60"/>
                        </group>
                    </sheet>
                    
                    <footer>
                        <button name="action_fix_invoices" 
                                type="object" 
                                string="Fix Invoices" 
                                class="btn-primary"/>
                        <button string="Cancel" 
                                class="btn-secondary" 
                                special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action for the wizard -->
        <record id="action_fix_early_invoices_wizard" model="ir.actions.act_window">
            <field name="name">Fix Early Invoices</field>
            <field name="res_model">fix.early.invoices.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
